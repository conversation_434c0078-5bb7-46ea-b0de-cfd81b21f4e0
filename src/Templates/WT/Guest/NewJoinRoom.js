import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Fire from "../../../config/Firebase.jsx";
import * as HostActions from "../../../Actions/HostAction";
import { connect } from "react-redux";
import { PostRequest } from "../../../Tools/helpers/api.js";
import banner from '../../../assets/img/guest_joining/Banner.png';
import * as Sessions from "../../../Actions/Sessions.js";
import * as ExceptionActions from "../../../Actions/Exception";
import VideoStream from "../../../components/VideoStream";

const NewJoinRoom = (props) => {
  const [name, setName] = useState("");
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [noRoom, setNoRoom] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Destructure essential props including LocalStream for video preview
  const { id, ClientData, SetClientData, SetConfig, LocalStream, SetLocalStream, DummyAudio } = props;

  useEffect(() => {

    console.log("!!!",id,props);
    // Check if client data already exists in localStorage
    if (!ClientData) {
      const clientData = localStorage.getItem(id);
      if (clientData) {
        SetClientData(JSON.parse(clientData));
        console.log("SetClientData from localStorage", clientData);
      }
    }

    // Check if the room exists
    Fire.firestore().collection("sessions").doc(id).get().then((doc) => {
      if (doc.exists) {
        if (doc.data().status === "ended") {
          navigate(`/salestool/feedback/${id}`);
        }
        SetConfig(doc.data());
        console.log("SetConfig from Firestore", doc.data());

        // Fetch lead name from roomdata.target.displayName like old join room
        if (doc.data().target && doc.data().target.displayName) {
          setName(doc.data().target.displayName);
        }
      } else {
        setNoRoom(true);
      }
      setLoading(false);
    }).catch(error => {
      console.error("Error checking room:", error);
      setNoRoom(true);
      setLoading(false);
    });

    // Initialize LocalStream for video preview like JoiningPageHost
    if (!LocalStream) {
      SetLocalStream(DummyAudio);
    }

  }, [id, navigate, SetConfig, ClientData, SetClientData, LocalStream, SetLocalStream, DummyAudio]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "name") {
      setName(value);
      // Clear error when user types
      if (errors.name) {
        setErrors(prev => ({ ...prev, name: null }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Validate name
    if (!name.trim()) {
      newErrors.name = "Name is required";
    } else if (!/^[a-zA-Z-,]+(\s{0,1}[a-zA-Z-, ])*$/.test(name)) {
      newErrors.name = "Contains only alphabets [A-Z]";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Set default email and phone like old join room
      const userEmail = `guest_${Date.now()}@example.com`;
      const userPhone = "";

      PostRequest({
        url: process.env.REACT_APP_API_BACKEND_API_URL + 'publicapis/CreateLead',
        body: {
          "name": name,
          "email": userEmail,
          "phone_number": userPhone,
          "session_id": id,
          source: 'sales_session',
          status: 'new',
        }
      }).then((response) => {
        localStorage.setItem(id, JSON.stringify({ GuestId: response.key, data: response }));
        SetClientData({ GuestId: response.key, data: response });

        // Redirect to room like old join room
        window.location = (`/salestool/guest/room/${id}`);
      }).catch(error => {
        console.error("Error creating lead:", error);
        setIsSubmitting(false);
      });
    } catch (error) {
      console.error("Error in form submission:", error);
      setIsSubmitting(false);
    }
  };



  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-blue-600">
        <div className="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
      </div>

    );
  }

  if (noRoom) {
    return (
      <div className="flex justify-center items-center h-screen bg-blue-600">
        <div className="bg-white p-6 rounded-lg shadow-lg">
          <h5 className="text-xl font-bold mb-2">Session not Found</h5>
          <p className="mb-4">Kindly re-check the invite link</p>
          <button
            onClick={() => window.history.back()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full bg-blue-600 flex flex-col items-center justify-center relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 bg-blue-600 opacity-80 z-0">
        <img
          src={banner}
          alt="Background"
          className="w-full h-full object-cover opacity-20"
        />
      </div>

      {/* Content container */}
      <div className="z-10 w-full max-w-md px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-white text-[20px] lg:text-[28px] font-normal mb-3">Property Experience Meeting</h1>
          <div className="w-48 mx-auto">
            <div className="border-b border-white/30 pb-1">
              <input
                type="text"
                name="name"
                value={name}
                onChange={handleChange}
                placeholder="Enter Name"
                autoComplete="off"
                className="bg-transparent text-white text-center w-full outline-none autofill:bg-transparent"
              />

            </div>
            {errors.name && (
              <p className="text-red-200 text-xs mt-1">{errors.name}</p>
            )}
          </div>
        </div>

        {/* Video preview like JoiningPageHost */}
        <div className="relative mb-4">
          <div className="rounded-lg w-[90%] mx-auto overflow-hidden bg-white/10 backdrop-blur-sm">
            <div className="aspect-video relative">
              <div className="w-full h-full rounded-xl bg-black">
                <div className="h-fit w-full">
                  {LocalStream ? <VideoStream /> : (
                    <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                      <div className="w-20 h-20 rounded-full bg-blue-500 flex items-center justify-center text-white text-2xl font-bold">
                        {name ? name.charAt(0).toUpperCase() : "G"}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Join button */}
        <div className="mb-4">
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="w-[80%] mx-auto bg-white text-[14px] gap-2 font-semibold py-2 px-4 rounded-md hover:bg-blue-50 transition-colors flex items-center justify-center"
          >
            {isSubmitting ? (
              <i className="fa fa-circle-o-notch fa-spin mr-2"></i>
            ) : (
              <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M7.69999 2.25H1.4C0.626801 2.25 0 2.92157 0 3.75V11.25C0 12.0784 0.626801 12.75 1.4 12.75H7.69999C8.47319 12.75 9.09999 12.0784 9.09999 11.25V3.75C9.09999 2.92157 8.47319 2.25 7.69999 2.25Z" fill="black"/>
<path d="M13.65 3.225C13.5439 3.15849 13.4234 3.12306 13.3006 3.12227C13.1777 3.12148 13.0568 3.15536 12.95 3.2205L10.5 4.713V10.3627L12.9255 12.0128C13.0316 12.0848 13.1539 12.1248 13.2794 12.1288C13.405 12.1327 13.5292 12.1004 13.6391 12.0352C13.749 11.97 13.8405 11.8744 13.904 11.7582C13.9675 11.6421 14.0006 11.5098 14 11.3752V3.87525C14.0001 3.74347 13.9678 3.61399 13.9064 3.49984C13.8449 3.38568 13.7565 3.29089 13.65 3.225Z" fill="black"/>
</svg>

            )}
            Join Meeting
          </button>
        </div>

        {/* Status message */}
        {/* <div className="text-center">
          <p className="text-white/80 text-sm">No one is in the call yet</p>
        </div> */}
      </div>
    </div>
  );
};

const mapStateToProps = (state) => {
  return {
    Config: state.Call.config,
    ProjectDetails: state.Sessions.projectDetails,
    SETUP: state.Call.SETUP,
    CanvasTrack: state.Call.CanvasTrack,
    ModalException: state.Exception.modalexception,
    DummyAudio: state.Call.DummyAudio,
    ClientData: state.Call.ClientData,
    SessionDetails: state.Sessions.sessions,
    LocalStream: state.Call.LocalStream,
    NetWorkSpeed: state.Call.NetWorkSpeed,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
  };
};

const mapDispatchToProps = {
  ...HostActions,
  ...Sessions,
  ...ExceptionActions,
};

export default connect(mapStateToProps, mapDispatchToProps)(NewJoinRoom);
